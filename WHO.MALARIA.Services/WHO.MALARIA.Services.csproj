﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <Content Include="..\WHO.MALARIA.Web\malaria-client\public\assets\locales\en\indicator-guide.en.json" Link="indicator-guide.en.json" />
    <Content Include="..\WHO.MALARIA.Web\malaria-client\public\assets\locales\en\indicators-responses.en.json" Link="indicators-responses.en.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="..\WHO.MALARIA.Web\malaria-client\public\assets\locales\en\ipti-indicator-guide.en.json" Link="ipti-indicator-guide.en.json" />
    <Content Include="..\WHO.MALARIA.Web\malaria-client\public\assets\locales\en\itn-mass-indicator-guide.en.json" Link="itn-mass-indicator-guide.en.json" />
    <Content Include="..\WHO.MALARIA.Web\malaria-client\public\assets\locales\en\itn-routine-indicator-guide.en.json" Link="itn-routine-indicator-guide.en.json" />
    <Content Include="..\WHO.MALARIA.Web\malaria-client\public\assets\locales\en\mda-indicator-guide.en.json" Link="mda-indicator-guide.en.json" />
    <Content Include="..\WHO.MALARIA.Web\malaria-client\public\assets\locales\en\smc-indicator-guide.en.json" Link="smc-indicator-guide.en.json" />
    <Content Include="..\WHO.MALARIA.Web\malaria-client\public\assets\locales\en\translation.en.json" Link="translation.en.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="..\WHO.MALARIA.Web\malaria-client\public\assets\locales\fr\indicator-guide.fr.json" Link="indicator-guide.fr.json" />
    <Content Include="..\WHO.MALARIA.Web\malaria-client\public\assets\locales\fr\indicators-responses.fr.json" Link="indicators-responses.fr.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="..\WHO.MALARIA.Web\malaria-client\public\assets\locales\fr\ipti-indicator-guide.fr.json" Link="ipti-indicator-guide.fr.json" />
    <Content Include="..\WHO.MALARIA.Web\malaria-client\public\assets\locales\fr\itn-mass-indicator-guide.fr.json" Link="itn-mass-indicator-guide.fr.json" />
    <Content Include="..\WHO.MALARIA.Web\malaria-client\public\assets\locales\fr\itn-routine-indicator-guide.fr.json" Link="itn-routine-indicator-guide.fr.json" />
    <Content Include="..\WHO.MALARIA.Web\malaria-client\public\assets\locales\fr\mda-indicator-guide.fr.json" Link="mda-indicator-guide.fr.json" />
    <Content Include="..\WHO.MALARIA.Web\malaria-client\public\assets\locales\fr\smc-indicator-guide.fr.json" Link="smc-indicator-guide.fr.json" />
    <Content Include="..\WHO.MALARIA.Web\malaria-client\public\assets\locales\fr\translation.fr.json" Link="translation.fr.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="Dapper" Version="2.1.24" />
    <PackageReference Include="Google.Apis.Analytics.v3" Version="1.64.0.1679" />
    <PackageReference Include="Google.Apis.AnalyticsReporting.v4" Version="1.64.0.2484" />
    <PackageReference Include="Hellang.Middleware.ProblemDetails" Version="6.5.1" />
    <PackageReference Include="IdentityModel" Version="6.2.0" />
    <PackageReference Include="MediatR" Version="12.2.0" />
    <PackageReference Include="Microsoft.Graph" Version="5.36.0" />
    <PackageReference Include="Microsoft.Graph.Core" Version="3.1.2" />
    <PackageReference Include="Serilog" Version="3.1.1" />
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="8.0.0" />
    <PackageReference Include="System.Diagnostics.DiagnosticSource" Version="8.0.0" />
    <PackageReference Include="System.Text.Json" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\WHO.MALARIA.Common\WHO.MALARIA.Common.csproj" />
    <ProjectReference Include="..\WHO.MALARIA.Database\WHO.MALARIA.Database.csproj" />
    <ProjectReference Include="..\WHO.MALARIA.DocumentManager\WHO.MALARIA.DocumentManager.csproj" />
    <ProjectReference Include="..\WHO.MALARIA.Domain\WHO.MALARIA.Domain.csproj" />
    <ProjectReference Include="..\WHO.MALARIA.Features\WHO.MALARIA.Features.csproj" />
  </ItemGroup>

</Project>

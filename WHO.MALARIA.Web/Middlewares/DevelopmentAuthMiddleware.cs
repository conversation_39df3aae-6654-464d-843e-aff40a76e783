using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Services.Commands.Identity;
using MediatR;
using WHO.MALARIA.Domain.Models.Identity;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Dtos;
using Microsoft.AspNetCore.Identity;
using System.Linq;

namespace WHO.MALARIA.Web.Middlewares
{
    /// <summary>
    /// Development-only middleware that automatically authenticates users to bypass Azure AD
    /// </summary>
    public class DevelopmentAuthMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<DevelopmentAuthMiddleware> _logger;
        private readonly AppSettings _appSettings;
        private readonly IMediator _mediator;
        private readonly UserManager<IdentityDto> _userManager;

        public DevelopmentAuthMiddleware(
            RequestDelegate next,
            ILogger<DevelopmentAuthMiddleware> logger,
            AppSettings appSettings,
            IMediator mediator,
            UserManager<IdentityDto> userManager)
        {
            _next = next;
            _logger = logger;
            _appSettings = appSettings;
            _mediator = mediator;
            _userManager = userManager;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // Only run in development environment
            if (!IsDevEnvironment() || !ShouldSkipAuth())
            {
                await _next(context);
                return;
            }

            // Skip authentication for static files and API endpoints that don't need auth
            if (ShouldSkipPath(context.Request.Path))
            {
                await _next(context);
                return;
            }

            // If user is not authenticated, auto-login as WHO admin
            if (!context.User.Identity.IsAuthenticated)
            {
                await AutoLoginAsWHOAdmin(context);
            }

            await _next(context);
        }

        private bool IsDevEnvironment()
        {
            return Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Development";
        }

        private bool ShouldSkipAuth()
        {
            return _appSettings?.AzureAD?.SkipAzureAuthentication == true;
        }

        private bool ShouldSkipPath(string path)
        {
            var skipPaths = new[]
            {
                "/css/", "/js/", "/images/", "/lib/", "/favicon.ico",
                "/api/", "/swagger", "/health", "/_framework/",
                "/idp/", "/signin-", "/signout-"
            };

            foreach (var skipPath in skipPaths)
            {
                if (path.StartsWith(skipPath, StringComparison.OrdinalIgnoreCase))
                    return true;
            }

            return false;
        }

        private async Task AutoLoginAsWHOAdmin(HttpContext context)
        {
            try
            {
                var email = _appSettings?.AzureAD?.WHOAdminEmail ?? "<EMAIL>";
                
                _logger.LogInformation($"Development mode: Auto-logging in as WHO admin with email: {email}");

                // Create or get the identity for the WHO admin user
                var identity = await EnsureWHOAdminExists(email);
                
                if (identity != null)
                {
                    // Create claims for the user
                    var claims = new List<Claim>
                    {
                        new Claim(ClaimTypes.NameIdentifier, identity.IdentityId.ToString()),
                        new Claim(ClaimTypes.Name, identity.UserName),
                        new Claim(ClaimTypes.Email, email),
                        new Claim("sub", identity.IdentityId.ToString()),
                        new Claim("preferred_username", identity.UserName),
                        new Claim("email", email),
                        new Claim("role", "WHOAdmin"),
                        new Claim("auth_method", "development_bypass")
                    };

                    var claimsIdentity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
                    var claimsPrincipal = new ClaimsPrincipal(claimsIdentity);

                    // Sign in the user
                    await context.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, claimsPrincipal);
                    
                    _logger.LogInformation($"Successfully auto-logged in WHO admin: {email}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to auto-login WHO admin in development mode");
            }
        }

        private async Task<IdentityDto> EnsureWHOAdminExists(string email)
        {
            try
            {
                // First try to find existing user
                var existingIdentity = await _userManager.FindByNameAsync(email);
                if (existingIdentity != null)
                {
                    _logger.LogInformation($"Found existing WHO admin identity for email: {email}");
                    return existingIdentity;
                }

                // If not found, create new identity
                _logger.LogInformation($"Creating new WHO admin identity for email: {email}");
                var identity = await _mediator.Send(new CreateIdentityWithUserCommand()
                {
                    Email = email
                });

                return identity;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to ensure WHO admin exists for email: {email}");
                return null;
            }
        }
    }
}

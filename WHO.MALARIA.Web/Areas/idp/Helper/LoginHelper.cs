﻿using Duende.IdentityServer.Models;
using Duende.IdentityServer.Services;
using Duende.IdentityServer.Stores;
using Microsoft.AspNetCore.Authentication;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WHO.MALARIA.Web.Areas.Idp.Models;

namespace WHO.MALARIA.Web.Areas.idp.Helper
{
    public class LoginHelper
    {
        private readonly IIdentityServerInteractionService _interaction;
        private readonly IAuthenticationSchemeProvider _schemeProvider;
        private readonly IClientStore _clientStore;

        public LoginHelper(
            IIdentityServerInteractionService interaction,
            IAuthenticationSchemeProvider schemeProvider,
            IClientStore clientStore)
        {
            _interaction = interaction;
            _schemeProvider = schemeProvider;
            _clientStore = clientStore;
        }

        internal async Task<LoginViewModel> BuildLoginViewModelAsync(LoginInputModel model)
        {
            LoginViewModel vm = await BuildLoginViewModelAsync(model.ReturnUrl);
            vm.Username = model.Username;
            vm.RememberLogin = model.RememberLogin;
            vm.IsValidUser = false;

            vm.LocalAuthenticationLabel = "External";
            vm.WindowsAuthenticationLabel = "Internal";
            vm.LocalAuthenticationEnabled = true;
            vm.WindowsAuthenticationEnabled = false;


            return vm;
        }

        internal async Task<LoginViewModel> BuildLoginViewModelAsync(string returnUrl)
        {
            AuthorizationRequest context = await _interaction.GetAuthorizationContextAsync(returnUrl);

            // Check if Azure authentication is bypassed in development
            bool isDevelopment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Development";
            // Note: We can't easily access AppSettings here, so we'll assume if it's development, local login should be enabled

            // this is meant to short circuit the UI and only trigger the one external IdP
            LoginViewModel vm = new LoginViewModel
            {
                ReturnUrl = returnUrl,
                Username = context?.LoginHint,
                LocalAuthenticationLabel = "External",
                WindowsAuthenticationLabel = "Internal",
                LocalAuthenticationEnabled = true,
                WindowsAuthenticationEnabled = false,
                EnableLocalLogin = isDevelopment ? true : AccountOptions.AllowLocalLogin, // Force enable local login in development
            };

            if (context?.IdP != null)
            {
                bool local = context.IdP == Duende.IdentityServer.IdentityServerConstants.LocalIdentityProvider;

                vm.EnableLocalLogin = local;

                if (!local && vm.WindowsAuthenticationEnabled)
                {
                    vm.ExternalProviders = new[] { new ExternalProvider { AuthenticationScheme = context.IdP } };
                }

                return vm;
            }

            vm.AllowRememberLogin = AccountOptions.AllowRememberLogin;

            await LoginViewModelWindowsAuthetication(context, vm);

            return vm;
        }

        /// <summary>
        /// Add external login providers when external login is enabled
        /// </summary>
        /// <param name="context"></param>
        /// <param name="vm"></param>
        /// <returns></returns>
        private async Task LoginViewModelWindowsAuthetication(AuthorizationRequest context, LoginViewModel vm)
        {
            bool isWindowsAthenticationEnabled = false;

            // when windows authentication is enabled
            if (isWindowsAthenticationEnabled)
            {

                IEnumerable<AuthenticationScheme> schemes = await _schemeProvider.GetAllSchemesAsync();

                List<ExternalProvider> providers = schemes
                    .Where(x => (x.Name.Equals(AccountOptions.WindowsAuthenticationSchemeName,
                        StringComparison.OrdinalIgnoreCase))
                    )
                    .Select(x => new ExternalProvider
                    {
                        DisplayName = x.DisplayName,
                        AuthenticationScheme = x.Name
                    }).ToList();

                bool allowLocal = true;

                if (context?.Client.ClientId != null)
                {
                    Client client = await _clientStore.FindEnabledClientByIdAsync(context?.Client.ClientId);
                    if (client != null)
                    {
                        allowLocal = client.EnableLocalLogin;

                        if (client.IdentityProviderRestrictions != null && client.IdentityProviderRestrictions.Any())
                        {
                            providers = providers.Where(provider =>
                                client.IdentityProviderRestrictions.Contains(provider.AuthenticationScheme)).ToList();
                        }
                    }
                }

                vm.ExternalProviders = providers.ToArray();

                // Force enable local login in development when Azure auth is bypassed
                bool isDevelopment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Development";
                vm.EnableLocalLogin = isDevelopment ? true : (allowLocal && AccountOptions.AllowLocalLogin);
            }
        }
    }
}

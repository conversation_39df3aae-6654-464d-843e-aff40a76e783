import Tabs from "../../../controls/Tabs";
import classNames from "classnames";
import React, { useEffect, useState } from "react";
import UserMessage from "../../../common/UserMessage";
import SubObjectives from "../../indicator-selection/SubObjectives";
import classes from "../../assessment.module.scss";
import HourglassEmptyIcon from "@mui/icons-material/HourglassEmpty";
import { useTranslation } from "react-i18next";
import {
  GetObjectiveModel,
  GetSubObjectiveModel,
} from "../../../../models/ScopeDefinitionModel";
import { assessmentService } from "../../../../services/assessmentService";
import { WHOTab } from "../../../controls/Tabs";
import scopedefinitionClasses from "../../indicator-selection/scopedefinition.module.scss";
import PriorityHighIcon from "@mui/icons-material/PriorityHigh";
import { GetIndicatorModel } from "../../../../models/IndicatorModel";
import { GetIndicatorsByStrategyIdRequestModel } from "../../../../models/RequestModels/DataCollectionRequestModel";
import { useLocation } from "react-router-dom";
import DataGrid from "../../../controls/DataGrid";
import {
  DeskReviewAssessmentResponseStatus,
  StrategiesEnum,
} from "../../../../models/Enums";
import { GridCellProps, GridColumnProps } from "@progress/kendo-react-grid";
import { Button, IconButton } from "@mui/material";
import { useNavigate } from "react-router-dom";
import { TabModel } from "../../../../models/TabModel";
import { Constants } from "../../../../models/Constants";
import { DiagramsStatusModel } from "../../../../models/DiagramsStatusModel";
import { useDispatch, useSelector } from "react-redux";
import { showUserGuide } from "../../../../redux/ducks/user-guide-drawer";
import { UtilityHelper } from "../../../../utils/UtilityHelper";

type DeskReviewContainerProps = {
  strategyId: string;
  strategyShortName?: string;
  strategies: Array<TabModel>;
};

/** Renders the Desk Review Container and all components underneath */
const DeskReviewContainer = (props: DeskReviewContainerProps) => {
  const { strategyId, strategyShortName, strategies } = props;
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location: any = useLocation();
  const assessmentId = location?.state?.assessmentId;
  document.title = t("app.DeskReviewTitle");
  const [currentIndex, setCurrentIndex] = useState<number>(0);
  const [subObjectiveId, setSubObjectiveId] = useState("");
  const [loading, setLoading] = useState(false);
  const dispatch = useDispatch();

  const [objectives, setObjectives] = useState<Array<GetObjectiveModel>>([]);
  const [subObjectives, setSubObjectives] = useState<
    Array<GetSubObjectiveModel>
  >([]);
  const [filteredSubObjectives, setfilteredSubObjectives] = useState<
    Array<GetSubObjectiveModel>
  >([]);

  const [indicators, setIndicators] = useState<Array<GetIndicatorModel>>([]);

  const diagramsStatus: DiagramsStatusModel = useSelector(
    (state: any) => state.scopeDefinition.diagramsStatus
  );

  const isObjectiveDiagramStatusCompleted =
    diagramsStatus?.objectiveDiagramStatus ===
      DeskReviewAssessmentResponseStatus.Completed &&
    (strategyId === StrategiesEnum.BurdenReduction.toLowerCase() ||
      strategyId === StrategiesEnum.Elimination.toLowerCase());
  const isSubObjectiveDiagramStatusCompleted =
    diagramsStatus?.subObjectiveDiagramStatus ===
      DeskReviewAssessmentResponseStatus.Completed &&
    (strategyId === StrategiesEnum.BurdenReduction.toLowerCase() ||
      strategyId === StrategiesEnum.Elimination.toLowerCase());

  const currentlanguage = UtilityHelper.getCookieValue("i18next");

  useEffect(() => {
    // check if user is coming from other screen or the response screen
    // if other screen then bind the objectives/sub-objectives/indicators
    // else fill the data from the stored state in location object.
    if (!canNavigateBack()) {
      bindObjectives();
    } else {
      onNavigateBack();
    }
  }, [currentlanguage]);

  useEffect(() => {
    getAssessmentIndicatorsForStrategy();
  }, [strategyId, currentlanguage]);

  const showObjective_3_UploadDiagramButton: boolean =
    objectives[currentIndex]?.id ===
    Constants.Common.TechnicalAndProcessObjectiveId;
  const showSubObjective_2_2_UploadDiagramButton: boolean =
    subObjectiveId === Constants.Common.InformationSystemSubObjectiveId;

  // check if user can be navigated back to where he has left
  const canNavigateBack = () =>
    location?.state?.currentIndex &&
    location?.state?.subObjectiveId &&
    location?.state?.objectives &&
    location?.state?.filteredSubObjectives &&
    location?.state?.subObjectives;

  // Helps user to redirect back to the same screen
  const onNavigateBack = async () => {
    // if user coming from response screen land it to respective screen
    // from where they had left
    if (canNavigateBack()) {
      setObjectives(location.state.objectives);
      setSubObjectives(location.state.subObjectives);
      setCurrentIndex(location.state.currentIndex);
      setfilteredSubObjectives(location.state.filteredSubObjectives);
      setSubObjectiveId(location.state.subObjectiveId);
    }
  };

  // Get assessment indicators for stragey
  const getAssessmentIndicatorsForStrategy = () => {
    if (!assessmentId || !strategyId) {
      return;
    }
    assessmentService
      .getIndicatorsByStragtegyId(
        new GetIndicatorsByStrategyIdRequestModel(assessmentId, strategyId)
      )
      .then((indicators: Array<GetIndicatorModel>) => {
        setIndicators(
          indicators.map((indicator: GetIndicatorModel, index: number) => {
            return {
              ...indicator,
              sr: ++index,
            };
          })
        );
      });
  };

  // bind objectives
  const bindObjectives = () => {
    setLoading(true);
    assessmentService
      .getObjectives()
      .then((objectives: Array<GetObjectiveModel>) => {
        setObjectives(objectives);
        const objectiveId = location?.state?.objectiveId;
        if (objectiveId) {
          bindSubObjectives(objectiveId);
        } else {
          bindSubObjectives(objectives[0]?.id);
          setCurrentIndex(0);
        }
        setLoading(false);
      });
  };

  // bind sub-objectives
  const bindSubObjectives = async (objectiveId: string) => {
    assessmentService
      .getSubObjectives(objectiveId)
      .then((subObjectives: Array<GetSubObjectiveModel>) => {
        setSubObjectives(subObjectives);
        const subObjectiveId = location?.state?.subObjectiveId;

        // When indicators of a particular subobjective are retrieved, the user assesses indicators from that list and
        // When the user clicks on the back or close button of an indicator, or if he clicks on another subobjective
        // the user gets to see the indicators of that particular subobjective which were previously selected.

        // Checking if subObjectiveId present in the subObjectives list or not
        const isSubObjectiveIdFound = subObjectives.some(
          (a) => a.id == subObjectiveId
        );

        if (subObjectiveId && isSubObjectiveIdFound) {
          setSubObjectiveId(subObjectiveId);
        } else {
          setSubObjectiveId(subObjectives[0]?.id);
          // adding subObjectiveId  in history
          if (subObjectiveId) {
            navigate(location?.pathname, {
              state: {
                ...location?.state,
                subObjectiveId: subObjectives[0]?.id,
              },
            });
          }
        }
        setfilteredSubObjectives(subObjectives);
        setLoading(false);
      });
  };

  // triggers whenever user changes the category tabs
  const onTabChange = (currentTabIndex: number) => {
    setLoading(true);
    const _subObjectives = subObjectives.filter(
      (subObj: GetSubObjectiveModel) =>
        subObj.objectiveId === objectives[currentTabIndex]?.id
    );

    setCurrentIndex(currentTabIndex);
    setfilteredSubObjectives(_subObjectives);
    bindSubObjectives(objectives[currentTabIndex].id);
  };

  // triggers whenever user changes the sub-category
  const onSubCategoryChange = (subObjectiveId: string) => {
    setSubObjectiveId(subObjectiveId);
  };

  // get the status for the indicator assessment response
  const getIndicatorStatus = (status: number) => {
    switch (status) {
      case DeskReviewAssessmentResponseStatus.InProgress:
        return t("Common.InProgress");
      case DeskReviewAssessmentResponseStatus.Completed:
        return t("Common.Completed");
      default:
        return t("Common.InProgress");
    }
  };

  // create column definition
  const getColDefs = (): Array<GridColumnProps> => {
    return [
      {
        title: "Status",
        width: "120",
        cell: (props: GridCellProps) => (
          <td>{getIndicatorStatus(props.dataItem["status"])}</td>
        ),
      },

      {
        title: "Indicator",
        sortable: true,
        cell: (props: GridCellProps) => (
          <td>
            <span>{props.dataItem["sequence"]}</span>
            <span className="ps-2 text-wrap">{props.dataItem["name"]} </span>
          </td>
        ),
      },
      {
        sortable: true,
        cell: (props: GridCellProps) => (
          <td>
            <Button
              className="btn app-btn-secondary grid-icon-button action-btn"
              onClick={() =>
                onAssessClick(
                  props.dataItem["id"],
                  props.dataItem["name"],
                  props.dataItem["sequence"],
                  props.dataItem["assessmentIndicatorId"],
                  props.dataItem["assessmentStrategyId"],
                  props.dataItem["status"]
                )
              }
            >
              {t("Assessment.DataCollection.GridColumn.Assess")}
            </Button>
          </td>
        ),
      },
    ];
  };

  // triggers whenever user takes an action to assess the Desk Review Indicator
  const onAssessClick = (
    id: string,
    name: string,
    sequence: string,
    assessmentIndicatorId: string,
    assessmentStrategyId: string,
    status: number
  ) => {
    const strategyIds = strategies?.map((strategy: TabModel) => strategy.id);
    const subObjective = subObjectives.find(
      (subObjective: GetSubObjectiveModel) => subObjective.id === subObjectiveId
    );
    const subObjectiveName = `${subObjective?.sequence} ${subObjective?.name}`;
    navigate("/assessment/data-collection/desk-review/indicator/response", {
      state: {
        assessmentId,
        indicatorId: id,
        strategyId,
        strategyShortName,
        indicatorName: name,
        indicators,
        strategyIds,
        sequence,
        country: location?.state.country,
        approach: location?.state.approach,
        status: location.state.status,
        assessmentResponseStatus: status,
        assessmentIndicatorId,
        assessmentStrategyId,
        // used to land user on same objective and sub-objective section(screen)
        currentIndex,
        objectiveId: objectives[currentIndex]?.id,
        subObjectiveId,
        objectives,
        filteredSubObjectives,
        subObjectives,
        subObjectiveName,
      },
    });

    dispatch(showUserGuide(true));

    return;
  };

  // triggers whenever user takes an action to upload the diagram for objective 3 for Desk Review
  const onUploadObj_3_DiagramClick = () => {
    const state = location?.state;
    navigate(
      "/assessment/data-collection/desk-review/objective-3/upload-diagram",
      {
        state: {
          ...state,
          // used to land user on same objective and sub-objective section(screen)
          currentIndex,
          objectiveId: objectives[currentIndex]?.id,
          subObjectiveId,
          objectives,
          filteredSubObjectives,
          subObjectives,
        },
      }
    );

    return;
  };

  // triggers whenever user takes an action to upload the diagram for sub-objective 2_2 for Desk Review
  const onUploadObj_2_2_DiagramClick = () => {
    const state = location?.state;
    navigate(
      "/assessment/data-collection/desk-review/objective-2_2/upload-diagram",
      {
        state: {
          ...state,
          // used to land user on same objective and sub-objective section(screen)
          currentIndex,
          objectiveId: objectives[currentIndex]?.id,
          subObjectiveId,
          objectives,
          filteredSubObjectives,
          subObjectives,
        },
      }
    );

    return;
  };

  // check if scope definition can be rendered
  const canRenderScopeDefinition = true;

  if (objectives.length === 0 && subObjectives.length === 0) {
    return (
      <div className="flex-container">
        <h6>
          <HourglassEmptyIcon />
          {t("Assessment.ScopeDefinition.IndicatorLoadingMessage")}
        </h6>
      </div>
    );
  }

  // get the filtered indicators by sub-objective
  const filteredIndicators = indicators.filter(
    (indicator: GetIndicatorModel) =>
      indicator.subObjectiveId === subObjectiveId
  );

  // show only those sub-objectives which has associated indicators
  // no indicators, don't show sub-objectives to
  const updatedSubObjectives = subObjectives?.filter(
    (subObjective: GetSubObjectiveModel) =>
      indicators?.filter(
        (indicator: GetIndicatorModel) =>
          indicator.subObjectiveId === subObjective.id
      ).length !== 0
  );

  return (
    <div className={classNames(classes.wrapper)}>
      <div className={classNames("mt-2", "rounded")}>
        <div className="cagtegory_header">
          <Tabs
            tabs={objectives.map((objective: GetObjectiveModel): WHOTab => {
              return { id: objective.id, label: objective.name };
            })}
            className={classNames("d-flex", "px-3", classes.tabWrapper)}
            activeTabIndex={currentIndex}
            onClick={onTabChange}
          >
            <div
              className={classNames(
                "d-flex",
                "align-items-stretch",
                "mt-5",
                classes.categoryWrapper
              )}
            >
              {canRenderScopeDefinition ? (
                <>
                  <div
                    className={classNames(
                      scopedefinitionClasses.subCategoriesWrapper
                    )}
                  >
                    {!loading ? (
                      <SubObjectives
                        objectiveId={objectives[currentIndex]?.id as string}
                        subObjectiveId={subObjectiveId}
                        subObjectives={updatedSubObjectives}
                        onClick={onSubCategoryChange}
                      />
                    ) : (
                      <UserMessage
                        className={classNames(classes.noRecordFound)}
                        content={t("Common.Loading")}
                      />
                    )}
                  </div>
                  <div
                    className={classNames(
                      "indicators-wrapper",
                      classes.indicatorsWrapper
                    )}
                  >
                    {showSubObjective_2_2_UploadDiagramButton &&
                      (strategyId ===
                        StrategiesEnum.BurdenReduction.toLowerCase() ||
                        strategyId ===
                          StrategiesEnum.Elimination.toLowerCase() ||
                        strategyId === StrategiesEnum.Both.toLowerCase()) && (
                        <div className="d-flex justify-content-between align-items-center mb-2">
                          <small className="fw-lighter fw-bold">
                            *{" "}
                            {t(
                              "Assessment.DataCollection.DeskReviewDiagram.Subobjective_2_2_UploadDiagramWarning"
                            )}
                          </small>
                          <div className="button-action-wrapper">
                            <div className="button-action-section d-flex align-items-center">
                              <small className="fw-lighter fw-bold mx-2">
                                {isSubObjectiveDiagramStatusCompleted
                                  ? t(
                                      "Assessment.DataCollection.DeskReviewDiagram.UploadDiagramCompleted"
                                    )
                                  : ""}
                              </small>
                              <Button
                                size="small"
                                className={classNames(
                                  "btn",
                                  "app-btn-secondary"
                                )}
                                onClick={() => onUploadObj_2_2_DiagramClick()}
                              >
                                {t(
                                  "Assessment.DataCollection.DeskReviewDiagram.UploadDiagram"
                                )}
                              </Button>
                            </div>
                          </div>
                        </div>
                      )}
                    {showObjective_3_UploadDiagramButton &&
                      (strategyId ===
                        StrategiesEnum.BurdenReduction.toLowerCase() ||
                        strategyId ===
                          StrategiesEnum.Elimination.toLowerCase() ||
                        strategyId === StrategiesEnum.Both.toLowerCase()) && (
                        <div className="d-flex justify-content-between align-items-center mb-2">
                          <small className="fw-lighter fw-bold">
                            *{" "}
                            {t(
                              "Assessment.DataCollection.DeskReviewDiagram.Objective_3_UploadDiagramWarning"
                            )}
                          </small>
                          <div className="button-action-wrapper">
                            <div className="button-action-section d-flex align-items-center">
                              <small className="fw-lighter fw-bold  mx-2">
                                {isObjectiveDiagramStatusCompleted
                                  ? t(
                                      "Assessment.DataCollection.DeskReviewDiagram.UploadDiagramCompleted"
                                    )
                                  : ""}
                              </small>
                              <Button
                                size="small"
                                className={classNames(
                                  "btn",
                                  "app-btn-secondary"
                                )}
                                onClick={() => onUploadObj_3_DiagramClick()}
                              >
                                {t(
                                  "Assessment.DataCollection.DeskReviewDiagram.UploadDiagram"
                                )}
                              </Button>
                            </div>
                          </div>
                        </div>
                      )}
                    {strategyId ? (
                      <DataGrid
                        className="k-grid-wrapper k-grid-action-wrapper k-desk-review"
                        columns={getColDefs()}
                        data={filteredIndicators}
                        selectable={{
                          enabled: true,
                          drag: false,
                          cell: true,
                          mode: "single",
                        }}
                        hasActionBtn={true}
                      />
                    ) : (
                      <UserMessage
                        renderContent={
                          <>
                            <PriorityHighIcon />
                            {t(
                              "Assessment.DataCollection.NoStrategySelectedMessage"
                            )}
                          </>
                        }
                      />
                    )}
                  </div>
                </>
              ) : (
                <UserMessage
                  className={classNames(classes.noRecordFound)}
                  content={t(
                    "Assessment.ScopeDefinition.IndicatorNoRecordFound"
                  )}
                />
              )}
            </div>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default DeskReviewContainer;

using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Database.IRepositories;
using IRepositoryDeskLevel = WHO.MALARIA.Database.IRepositories.DQA.DeskLevel;
using IRepositoryElimination = WHO.MALARIA.Database.IRepositories.DQA.Elimination;
using WHO.MALARIA.Database.Repositories;
using RepositoryDeskLevel = WHO.MALARIA.Database.Repositories.DQA.DeskLevel;
using RepositoryElimination = WHO.MALARIA.Database.Repositories.DQA.Elimination;
using WHO.MALARIA.Domain.Models;
using static WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Database
{
    /// <summary>
    /// This class is used to perform db operations like insert/update/delete/get
    /// </summary>
    public class UnitOfWork : IUnitOfWork
    {
        private readonly MalariaDbContext _malariaDbContext;
        private readonly IDbManager _dbManager;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public UnitOfWork(IDbManager dbManager, MalariaDbContext malariaDbContextFactory, IHttpContextAccessor httpContextAccessor)
        {
            _malariaDbContext = malariaDbContextFactory;
            _dbManager = dbManager;
            _httpContextAccessor = httpContextAccessor ?? throw new ArgumentNullException();
        }

        public Task<int> CommitAsync(CancellationToken cancellationToken = default(CancellationToken))
        {
            ClaimsPrincipal user = _httpContextAccessor.HttpContext.User;
            Guid? userId = null;
            if (user.Identity.IsAuthenticated)
            {
                userId = Guid.Parse(user.Claims.Single(c => c.Type == IdentityClaims.PrimarySidClaim).Value);
            }

            this.SetCommonFields(userId);

            return _malariaDbContext.SaveChangesAsync(cancellationToken);
        }

        public void Rollback()
        {
            _malariaDbContext.Dispose();
        }

        /// <summary>
        /// Set common fields like createdBy, updatedby
        /// </summary>
        /// <param name="userId">Logged in user id</param>
        private void SetCommonFields(Guid? userId)
        {

            new AuditHelper(_malariaDbContext).AddAuditLogs(userId);

            //Get the newly added entities from DB context
            IEnumerable<ModelBase> newEntities = _malariaDbContext.ChangeTracker.Entries()
                                                                  .Where(e => e.State == EntityState.Added)
                                                                  .Select(e => e.Entity)
                                                                  .OfType<ModelBase>();

            //Get the modified entities from DB context
            IEnumerable<ModelBase> modifiedEntiies = _malariaDbContext.ChangeTracker.Entries()
                                                                      .Where(e => e.State == EntityState.Modified)
                                                                      .Select(e => e.Entity)
                                                                      .OfType<ModelBase>();

            //Set created by and created on fields of entity if entity is newly added
            foreach (ModelBase entity in newEntities)
            {
                entity.CreatedBy = userId;
                entity.CreatedAt = DateTime.UtcNow;
                entity.UpdatedBy = userId;
                entity.UpdatedAt = DateTime.UtcNow;
            }

            //Set modify by and modified on fields of entity if entity is modified
            foreach (ModelBase entity in modifiedEntiies)
            {
                entity.UpdatedBy = userId;
                entity.UpdatedAt = DateTime.UtcNow;
            }
        }

        #region Private Variables

        private IUserRepository _userRepository;
        private IIdentityRepository _identityRepository;
        private IUserCountryAccessRepository _userCountryAccessRepository;
        private IAssessmentRepository _assessmentRepository;
        private IIndicatorRepository _indicatorRepository;
        private IAssessmentUserRepository _assessmentUserRepository;
        private IEmailTempalteRepository _emailTemplateRepository;
        private ICountryRepository _countryRepository;
        private IAssessmentStatusRepository _assessmentStatusRepository;
        private IAssessmentDRResponseRepository _assessmentDRResponseRepository;
        private ISLVariableCompletenessResponseRepository _sLVariableCompletenessResponseRepository;
        private ISLVariableDataResponseRepository _sLVariableDataResponseRepository;
        private IDQARepository _dqaRepository;
        private IServiceLevelRepository _serviceLevelRepository;
        private IServiceLevelRegisterRepository _serviceLevelRegisterRepository;
        private IServiceLevelVariableRepository _serviceLevelVariableRepository;
        private IObjectiveDiagramRepository _objectiveDiagramRepository;
        private ISubObjectiveDiagramRepository _subObjectiveDiagramRepository;
        private IResponseDocumentRepository _responseDocumentRepository;
        private IAssessmentRespondentTypeRepository _assessmentRespondentTypeRepository;
        private IAssessmentQuestionRepository _assessmentQuestionRepository;
        private IAnalyticalOutputRepository _analyticalOutputRepository;
        private IRepositoryDeskLevel.IDeskLevelRepository _deskLevelRepository;
        private IRepositoryDeskLevel.IDataSourceRepository _dataSourceRepository;
        private IRepositoryDeskLevel.IVariableMappingRepository _variableMappingRepository;
        private IRepositoryDeskLevel.IDeskLevelDataSystem1Repository _deskLevelDataSystem1Repository;
        private IRepositoryDeskLevel.IDeskLevelDataSystem2Repository _deskLevelDataSystem2Repository;
        private IHealthFacilityRepository _healthFacilityRepository;
        private IShellTableRepository _shellTableRepository;
        private IRepositoryDeskLevel.ISummaryRepository _summaryRepository;
        private IRepositoryDeskLevel.ISummaryDataQualityResultReasonRepository _summaryDataQualityResultReasonRepository;
        private IRepositoryDeskLevel.IDQADataSourceRepository _dqaDataSourceRepository;
        private IRepositoryElimination.ISummaryRepository _eliminationSummaryRepository;
        private IRepositoryElimination.ISummaryDataQualityResultReasonRepository _eliminationSummaryDataQualityReasonRepository;
        private IScoreCardRepository _scoreCardRepository;
        private IDataAnalysisReportRepository _dataAnalysisReportRepository;
        private IShellTableReportRepository _shellTableReportRepository;
        private IRegionRepository _regionRepository;

        #endregion

        #region Public Repositories

        public IRegionRepository RegionRepository => _regionRepository ?? (_regionRepository = new RegionRepository(_dbManager, _malariaDbContext));
        public IUserRepository UserRepository => _userRepository ?? (_userRepository = new UserRepository(_dbManager, _malariaDbContext, _httpContextAccessor));
        public IIdentityRepository IdentityRepository => _identityRepository ?? (_identityRepository = new IdentityRepository(_dbManager, _malariaDbContext));
        public IUserCountryAccessRepository UserCountryAccessRepository => _userCountryAccessRepository ?? (_userCountryAccessRepository = new UserCountryAccessRepository(_dbManager, _malariaDbContext));
        public IAssessmentRepository AssessmentRepository => _assessmentRepository ?? (_assessmentRepository = new AssessmentRepository(_dbManager, _malariaDbContext, _httpContextAccessor));
        public IIndicatorRepository IndicatorRepository => _indicatorRepository ?? (_indicatorRepository = new IndicatorRepository(_dbManager, _malariaDbContext, _httpContextAccessor));
        public IAssessmentUserRepository AssessmentUserRepository => _assessmentUserRepository ?? (_assessmentUserRepository = new AssessmentUserRepository(_dbManager, _malariaDbContext));
        public IEmailTempalteRepository EmailTemplateRepository => _emailTemplateRepository ?? (_emailTemplateRepository = new EmailTemplateRepository(_dbManager, _malariaDbContext));
        public ICountryRepository CountryRepository => _countryRepository ?? (_countryRepository = new CountryRepository(_dbManager, _malariaDbContext));
        public IAssessmentStatusRepository AssessmentStatusRepository => _assessmentStatusRepository ?? (_assessmentStatusRepository = new AssessmentStatusRepository(_dbManager, _malariaDbContext));
        public IAssessmentDRResponseRepository AssessmentDRResponseRepository => _assessmentDRResponseRepository ?? (_assessmentDRResponseRepository = new AssessmentDRResponseRepository(_dbManager, _malariaDbContext));
        public ISLVariableCompletenessResponseRepository VariableCompletenessResponseRepository => _sLVariableCompletenessResponseRepository ?? (_sLVariableCompletenessResponseRepository = new SLVariableCompletenessResponseRepository(_dbManager, _malariaDbContext));
        public ISLVariableDataResponseRepository VariableDataResponseRepository => _sLVariableDataResponseRepository ?? (_sLVariableDataResponseRepository = new SLVariableDataResponseRepository(_dbManager, _malariaDbContext));
        public IDQARepository DQARepository => _dqaRepository ?? (_dqaRepository = new DQARepository(_dbManager, _malariaDbContext, _httpContextAccessor));
        public IServiceLevelRepository ServiceLevelRepository => _serviceLevelRepository ?? (_serviceLevelRepository = new ServiceLevelRepository(_dbManager, _malariaDbContext));
        public IServiceLevelVariableRepository ServiceLevelVariableRepository => _serviceLevelVariableRepository ?? (_serviceLevelVariableRepository = new ServiceLevelVariableRepository(_dbManager, _malariaDbContext));
        public IServiceLevelRegisterRepository ServiceLevelRegisterRepository => _serviceLevelRegisterRepository ?? (_serviceLevelRegisterRepository = new ServiceLevelRegisterRepository(_dbManager, _malariaDbContext));
        public IObjectiveDiagramRepository ObjectiveDiagramRepository => _objectiveDiagramRepository ?? (_objectiveDiagramRepository = new ObjectiveDiagramRepository(_dbManager, _malariaDbContext));
        public ISubObjectiveDiagramRepository SubObjectiveDiagramRepository => _subObjectiveDiagramRepository ?? (_subObjectiveDiagramRepository = new SubObjectiveDiagramRepository(_dbManager, _malariaDbContext));
        public IResponseDocumentRepository ResponseDocumentRepository => _responseDocumentRepository ?? (_responseDocumentRepository = new ResponseDocumentRepository(_dbManager, _malariaDbContext));
        public IAssessmentRespondentTypeRepository AssessmentRespondentTypeRepository => _assessmentRespondentTypeRepository ?? (_assessmentRespondentTypeRepository = new AssessmentRespondentTypeRepository(_dbManager, _malariaDbContext));
        public IAssessmentQuestionRepository AssessmentQuestionRepository => _assessmentQuestionRepository ?? (_assessmentQuestionRepository = new AssessmentQuestionRepository(_dbManager, _malariaDbContext, _httpContextAccessor));
        public IAnalyticalOutputRepository AnalyticalOutputRepository => _analyticalOutputRepository ?? (_analyticalOutputRepository = new AnalyticalOutputRepository(_dbManager, _malariaDbContext, _httpContextAccessor));
        public IRepositoryDeskLevel.IDeskLevelRepository DeskLevelRepository => _deskLevelRepository ?? (_deskLevelRepository = new RepositoryDeskLevel.DeskLevelRepository(_dbManager, _malariaDbContext));
        public IRepositoryDeskLevel.IDataSourceRepository DataSourceRepository => _dataSourceRepository ?? (_dataSourceRepository = new RepositoryDeskLevel.DataSourceRepository(_malariaDbContext));
        public IRepositoryDeskLevel.IVariableMappingRepository VariableMappingRepository => _variableMappingRepository ?? (_variableMappingRepository = new RepositoryDeskLevel.VariableMappingRepository(_malariaDbContext));
        public IRepositoryDeskLevel.IDeskLevelDataSystem1Repository DeskLevelDataSystem1Repository => _deskLevelDataSystem1Repository ?? (_deskLevelDataSystem1Repository = new RepositoryDeskLevel.DeskLevelDataSystem1Repository(_dbManager, _malariaDbContext));
        public IRepositoryDeskLevel.IDeskLevelDataSystem2Repository DeskLevelDataSystem2Repository => _deskLevelDataSystem2Repository ?? (_deskLevelDataSystem2Repository = new RepositoryDeskLevel.DeskLevelDataSystem2Repository(_dbManager, _malariaDbContext));
        public IHealthFacilityRepository HealthFacilityRepository => _healthFacilityRepository ?? (_healthFacilityRepository = new HealthFacilityRepository(_dbManager, _malariaDbContext));
        public IShellTableRepository ShellTableRepository => _shellTableRepository ?? (_shellTableRepository = new ShellTableRepository(_dbManager, _malariaDbContext));
        public IRepositoryDeskLevel.ISummaryRepository SummaryRepository => _summaryRepository ?? (_summaryRepository = new RepositoryDeskLevel.SummaryRepository(_dbManager, _malariaDbContext));
        public IRepositoryDeskLevel.ISummaryDataQualityResultReasonRepository SummaryDataQualityResultReasonRepository => _summaryDataQualityResultReasonRepository ?? (_summaryDataQualityResultReasonRepository = new RepositoryDeskLevel.SummaryDataQualityResultReasonRepository(_dbManager, _malariaDbContext));
        public IRepositoryDeskLevel.IDQADataSourceRepository DQADataSourceRepository => _dqaDataSourceRepository ?? (_dqaDataSourceRepository = new RepositoryDeskLevel.DQADataSourceRepository(_dbManager, _malariaDbContext));
        public IRepositoryElimination.ISummaryRepository EliminationSummaryRepository => _eliminationSummaryRepository ?? (_eliminationSummaryRepository = new RepositoryElimination.SummaryRepository(_dbManager, _malariaDbContext));
        public IRepositoryElimination.ISummaryDataQualityResultReasonRepository EliminationSummaryDataQualityReasonRepository => _eliminationSummaryDataQualityReasonRepository ?? (_eliminationSummaryDataQualityReasonRepository = new RepositoryElimination.SummaryDataQualityResultReasonRepository(_dbManager, _malariaDbContext));

        public IScoreCardRepository ScoreCardRepository => _scoreCardRepository ?? (_scoreCardRepository = new ScoreCardRepository(_dbManager, _malariaDbContext, _httpContextAccessor));
        public IDataAnalysisReportRepository DataAnalysisReportRepository => _dataAnalysisReportRepository ?? (_dataAnalysisReportRepository = new DataAnalysisReportRepository(_dbManager, _malariaDbContext));
        public IShellTableReportRepository ShellTableReportRepository => _shellTableReportRepository ?? (_shellTableReportRepository = new ShellTableReportRepository(_dbManager, _malariaDbContext));
        #endregion
    }
}
